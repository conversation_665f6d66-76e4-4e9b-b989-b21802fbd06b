// 用户管理与认证相关API服务

import api from '@/api/core/api';
import type {
  UserListRequest,
  UserListResponse,
  CreateUserRequest,
  CreateUserResponse,
  UpdateUserRequest,
  DeleteUserRequest,
  BatchUserOperationRequest,
  GetUserDetailRequest,
  UserDetailResponse,
  UserStatsRequest,
  UserStatsResponse,
  UserInfo,
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  GetUserInfoRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest
} from '../types/user';

export const userService = {
  // ==================== 认证相关方法 ====================

  /**
   * 用户登录
   * @param username 用户名
   * @param password 密码
   * @returns Promise<LoginResponse>
   */
  async login(username: string, password: string): Promise<LoginResponse> {
    return await api<LoginResponse>({
      action: 'login',
      username,
      password
    } as LoginRequest);
  },

  /**
   * 用户登出
   * @returns Promise<void>
   */
  async logout(): Promise<void> {
    return await api({
      action: 'logout'
    } as LogoutRequest);
  },

  /**
   * 获取当前用户信息
   * @returns Promise<UserInfo>
   */
  async getUserInfo(): Promise<UserInfo> {
    return await api<UserInfo>({
      action: 'getUserInfo'
    } as GetUserInfoRequest);
  },

  /**
   * 刷新访问令牌
   * @param refreshToken 刷新令牌
   * @returns Promise<RefreshTokenResponse>
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    return await api<RefreshTokenResponse>({
      action: 'refreshToken',
      refreshToken
    } as RefreshTokenRequest);
  },

  /**
   * 修改密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns Promise<void>
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    return await api({
      action: 'changePassword',
      oldPassword,
      newPassword
    } as ChangePasswordRequest);
  },

  // ==================== 用户管理相关方法 ====================

  /**
   * 获取用户列表
   * @param params 查询参数
   * @returns Promise<UserListResponse>
   */
  async getUserList(params: {
    page?: number;
    size?: number;
    keyword?: string;
    role?: string;
    status?: 'active' | 'inactive' | 'all';
  } = {}): Promise<UserListResponse> {
    return await api<UserListResponse>({
      action: 'getUserList',
      page: 1,
      size: 10,
      ...params
    } as UserListRequest);
  },

  /**
   * 创建用户
   * @param userData 用户数据
   * @returns Promise<CreateUserResponse>
   */
  async createUser(userData: {
    username: string;
    email: string;
    password: string;
    role: 'admin' | 'user' | 'guest';
    avatar?: string;
  }): Promise<CreateUserResponse> {
    return await api<CreateUserResponse>({
      action: 'createUser',
      ...userData
    } as CreateUserRequest);
  },

  /**
   * 更新用户信息
   * @param userId 用户ID
   * @param updateData 更新数据
   * @returns Promise<void>
   */
  async updateUser(
    userId: string, 
    updateData: {
      username?: string;
      email?: string;
      role?: 'admin' | 'user' | 'guest';
      avatar?: string;
      status?: 'active' | 'inactive';
    }
  ): Promise<void> {
    return await api({
      action: 'updateUser',
      userId,
      ...updateData
    } as UpdateUserRequest);
  },

  /**
   * 删除用户
   * @param userId 用户ID
   * @returns Promise<void>
   */
  async deleteUser(userId: string): Promise<void> {
    return await api({
      action: 'deleteUser',
      userId
    } as DeleteUserRequest);
  },

  /**
   * 批量用户操作
   * @param operation 操作类型
   * @param userIds 用户ID列表
   * @returns Promise<void>
   */
  async batchUserOperation(
    operation: 'delete' | 'activate' | 'deactivate',
    userIds: string[]
  ): Promise<void> {
    return await api({
      action: 'batchUserOperation',
      operation,
      userIds
    } as BatchUserOperationRequest);
  },

  /**
   * 获取用户详情
   * @param userId 用户ID
   * @returns Promise<UserDetailResponse>
   */
  async getUserDetail(userId: string): Promise<UserDetailResponse> {
    return await api<UserDetailResponse>({
      action: 'getUserDetail',
      userId
    } as GetUserDetailRequest);
  },

  /**
   * 获取用户统计信息
   * @returns Promise<UserStatsResponse>
   */
  async getUserStats(): Promise<UserStatsResponse> {
    return await api<UserStatsResponse>({
      action: 'getUserStats'
    } as UserStatsRequest);
  },

  /**
   * 搜索用户
   * @param keyword 搜索关键词
   * @param limit 结果限制数量
   * @returns Promise<UserInfo[]>
   */
  async searchUsers(keyword: string, limit: number = 10): Promise<UserInfo[]> {
    const result = await this.getUserList({
      keyword,
      size: limit,
      page: 1
    });
    return result.users;
  }
};