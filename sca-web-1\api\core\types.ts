// 认证API请求参数接口
export interface AuthApiParams {
  action: string;             // 认证操作类型
  [key: string]: any;         // 其他参数直接在根级别
}

// 业务API请求参数接口
export interface BusinessApiParams {
  resource: string;           // 资源标识
  method_name: string;        // 方法名称
  data?: Record<string, any>; // 其他参数包裹在data中
}

// API请求参数联合类型
export type ApiParams = AuthApiParams | BusinessApiParams;

// API统一响应格式接口
export interface ApiResponse<T = any> {
  code: number;        // 业务状态码 (401/403/500等)
  message: string;     // 响应消息
  data: T;            // 实际返回数据
}

// 业务状态码枚举
export enum BusinessCode {
  SUCCESS = 200,       // 成功
  UNAUTHORIZED = 401,  // 未授权
  FORBIDDEN = 403,     // 禁止访问
  SERVER_ERROR = 500   // 服务器错误
}

