// 软著任务状态类型
export type CopyrightTaskStatus = "生成中" | "已完成" | "生成失败";

// 软著任务接口
export interface CopyrightTask {
  _id: string;
  user_id?: string;
  customer_name?: string;
  name?: string;
  description?: string;
  status?: CopyrightTaskStatus;
  document_key?: string;
  create_at?: number;
  completed_at?: number;
  is_deleted?: boolean;
  download_url?: string; // API动态生成的下载链接
}

// 软著任务列表响应接口
export interface CopyrightTaskListResponse {
  total: number;
  page: number;
  page_size: number;
  data: CopyrightTask[];
}

// 软著任务创建参数接口
export interface CreateCopyrightTaskParams {
  name: string;
  description: string;
}

// 软著任务查询参数接口
export interface QueryCopyrightTaskParams {
  page?: number;
  page_size?: number;
}
