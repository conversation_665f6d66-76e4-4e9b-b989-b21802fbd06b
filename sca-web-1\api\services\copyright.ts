"use client";

import api from "../core/api";
import { 
  CopyrightTaskListResponse, 
  CreateCopyrightTaskParams, 
  QueryCopyrightTaskParams 
} from "../types/copyright";

/**
 * 软著任务服务
 */
export const copyrightService = {
  /**
   * 创建软著任务
   * @param name - 任务名称
   * @param description - 任务描述
   * @returns Promise<void> 无返回值
   */
  create: async (name: string, description: string): Promise<void> => {
    return await api({
      resource: "copyright_task",
      method_name: "create",
      name,
      description,
    });
  },

  /**
   * 查询软著任务列表
   * @param params 查询参数
   * @param params.page 页码，默认为1
   * @param params.page_size 每页数量，默认为10
   * @returns Promise<CopyrightTaskListResponse> 包含分页信息和任务列表的对象
   */
  queryList: async (params: QueryCopyrightTaskParams = {}): Promise<CopyrightTaskListResponse> => {
    return await api({
      resource: "copyright_task",
      method_name: "query_list",
      page: 1,
      page_size: 10,
      ...params
    });
  },
};
